import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;
import net.sf.jsqlparser.statement.delete.Delete;

public class DebugTest {
    public static void main(String[] args) {
        String[] testQueries = {
            "UPDATE users SET name = '<PERSON>'",
            "DELETE FROM users",
            "SELECT LOAD_FILE('/etc/passwd') FROM users",
            "SELECT * FROM users WHERE id = SLEEP(5)"
        };
        
        for (String sql : testQueries) {
            try {
                Statement statement = CCJSqlParserUtil.parse(sql);
                System.out.println("SQL: " + sql);
                System.out.println("Statement type: " + statement.getClass().getSimpleName());
                
                if (statement instanceof Update) {
                    Update update = (Update) statement;
                    System.out.println("WHERE clause: " + update.getWhere());
                } else if (statement instanceof Delete) {
                    Delete delete = (Delete) statement;
                    System.out.println("WHERE clause: " + delete.getWhere());
                } else if (statement instanceof Select) {
                    Select select = (Select) statement;
                    System.out.println("SELECT body: " + select.getSelectBody());
                }
                System.out.println("---");
            } catch (JSQLParserException e) {
                System.out.println("Parse error for '" + sql + "': " + e.getMessage());
            }
        }
    }
}
