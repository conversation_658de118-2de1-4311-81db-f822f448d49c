import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import java.lang.reflect.Method;

public class SelectBodyDebugTest {
    public static void main(String[] args) {
        String sql = "SELECT LOAD_FILE('/etc/passwd') FROM users";

        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                Select select = (Select) statement;
                Object selectBody = select.getSelectBody();

                System.out.println("SelectBody type: " + selectBody.getClass().getSimpleName());
                System.out.println("SelectBody: " + selectBody);

                System.out.println("\nAvailable accept methods on SelectBody:");
                Method[] methods = selectBody.getClass().getMethods();
                for (Method method : methods) {
                    if (method.getName().equals("accept")) {
                        System.out.println("  " + method.getName() + "(" + java.util.Arrays.toString(method.getParameterTypes()) + ")");
                    }
                }
            }
        } catch (JSQLParserException e) {
            System.out.println("Parse error: " + e.getMessage());
        }
    }
}
