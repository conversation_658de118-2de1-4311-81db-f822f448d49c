package com.siemens.spine.logic.validator.query.visitor;

import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.ExpressionVisitorAdapter;
import net.sf.jsqlparser.expression.Function;

import java.util.HashSet;
import java.util.Set;

@Slf4j
public class FunctionCallVisitor extends ExpressionVisitorAdapter<Void> {

    private static final Set<String> DANGEROUS_FUNCTIONS = Set.of(
            "LOAD_FILE", "INTO_OUTFILE", "INTO_DUMPFILE",
            "SYSTEM", "CMD", "EVAL", "EXEC", "EXECUTE",
            "SP_EXECUTESQL", "XP_CMDSHELL", "SLEEP",
            "BENCHMARK", "WAITFOR", "DELAY"
    );

    private final Set<String> foundDangerousFunctions = new HashSet<>();

    @Override
    public void visit(Function function) {
        String functionName = function.getName().toUpperCase();
        log.debug("Visiting function: {}", functionName);

        if (DANGEROUS_FUNCTIONS.contains(functionName)) {
            foundDangerousFunctions.add(functionName);
            log.debug("Found dangerous function: {}", functionName);
        }

        // Check function parameters recursively
        if (function.getParameters() != null && function.getParameters().getExpressions() != null) {
            function.getParameters().getExpressions().forEach(expr -> expr.accept(this));
        }

        // Continue with parent visitor behavior to ensure all expressions are visited
        super.visit(function);
    }

    public boolean hasDangerousFunctions() {
        return !foundDangerousFunctions.isEmpty();
    }

    public Set<String> getDangerousFunctions() {
        return foundDangerousFunctions;
    }

}
