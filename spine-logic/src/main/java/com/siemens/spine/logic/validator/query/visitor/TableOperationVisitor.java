package com.siemens.spine.logic.validator.query.visitor;

import lombok.Getter;
import net.sf.jsqlparser.statement.StatementVisitorAdapter;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.update.Update;

import java.util.Set;

@Getter
public class TableOperationVisitor extends StatementVisitorAdapter {

    private final Set<String> allowedTables;
    private boolean allowed = true;
    private String denialReason = "";

    public TableOperationVisitor(Set<String> allowedTables) {
        this.allowedTables = allowedTables;
    }

    @Override
    public void visit(Update update) {
        String tableName = update.getTable().getName();
        allowed = false;
        denialReason = "No UPDATE permission on table: " + tableName;
    }

    @Override
    public void visit(Delete delete) {
        String tableName = delete.getTable().getName();
        allowed = false;
        denialReason = "No DELETE permission on table: " + tableName;
    }

    @Override
    public void visit(Insert insert) {
        String tableName = insert.getTable().getName();
        allowed = false;
        denialReason = "No INSERT permission on table: " + tableName;
    }

}