import net.sf.jsqlparser.statement.StatementVisitor;
import net.sf.jsqlparser.statement.StatementVisitorAdapter;
import java.lang.reflect.Method;

public class VisitorInterfaceTest {
    public static void main(String[] args) {
        System.out.println("=== StatementVisitor Interface Methods ===");
        Method[] methods = StatementVisitor.class.getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("visit")) {
                System.out.println(method.getName() + "(" + java.util.Arrays.toString(method.getParameterTypes()) + ") -> " + method.getReturnType());
            }
        }
        
        System.out.println("\n=== StatementVisitorAdapter Methods ===");
        Method[] adapterMethods = StatementVisitorAdapter.class.getMethods();
        for (Method method : adapterMethods) {
            if (method.getName().startsWith("visit")) {
                System.out.println(method.getName() + "(" + java.util.Arrays.toString(method.getParameterTypes()) + ") -> " + method.getReturnType());
            }
        }
        
        System.out.println("\n=== StatementVisitorAdapter Class Info ===");
        System.out.println("Superclass: " + StatementVisitorAdapter.class.getSuperclass());
        System.out.println("Interfaces: " + java.util.Arrays.toString(StatementVisitorAdapter.class.getInterfaces()));
    }
}
