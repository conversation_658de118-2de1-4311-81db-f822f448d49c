import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.StatementVisitorAdapter;
import net.sf.jsqlparser.statement.update.Update;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.select.Select;

public class VisitorDebugTest {
    
    static class TestVisitor extends StatementVisitorAdapter {
        private boolean visited = false;
        private String visitedType = "";

        @Override
        public void visit(Update update) {
            System.out.println("Visiting UPDATE (void): " + update);
            System.out.println("WHERE clause: " + update.getWhere());
            visited = true;
            visitedType = "UPDATE";
        }

        @Override
        public Object visit(Update update, Object context) {
            System.out.println("Visiting UPDATE (Object): " + update + ", context: " + context);
            System.out.println("WHERE clause: " + update.getWhere());
            visited = true;
            visitedType = "UPDATE";
            return super.visit(update, context);
        }

        @Override
        public void visit(Delete delete) {
            System.out.println("Visiting DELETE (void): " + delete);
            System.out.println("WHERE clause: " + delete.getWhere());
            visited = true;
            visitedType = "DELETE";
        }

        @Override
        public Object visit(Delete delete, Object context) {
            System.out.println("Visiting DELETE (Object): " + delete + ", context: " + context);
            System.out.println("WHERE clause: " + delete.getWhere());
            visited = true;
            visitedType = "DELETE";
            return super.visit(delete, context);
        }

        @Override
        public void visit(Select select) {
            System.out.println("Visiting SELECT (void): " + select);
            visited = true;
            visitedType = "SELECT";
        }

        @Override
        public Object visit(Select select, Object context) {
            System.out.println("Visiting SELECT (Object): " + select + ", context: " + context);
            visited = true;
            visitedType = "SELECT";
            return super.visit(select, context);
        }
        
        public boolean wasVisited() {
            return visited;
        }
        
        public String getVisitedType() {
            return visitedType;
        }
    }
    
    public static void main(String[] args) {
        String[] testQueries = {
            "UPDATE users SET name = 'John Doe'",
            "DELETE FROM users",
            "SELECT * FROM users"
        };
        
        for (String sql : testQueries) {
            try {
                System.out.println("\n=== Testing: " + sql + " ===");
                Statement statement = CCJSqlParserUtil.parse(sql);
                System.out.println("Statement type: " + statement.getClass().getSimpleName());
                
                TestVisitor visitor = new TestVisitor();
                System.out.println("Before accept - visited: " + visitor.wasVisited());
                
                // Try the accept method
                try {
                    statement.accept(visitor);
                    System.out.println("Accept method called successfully");
                } catch (Exception e) {
                    System.out.println("Accept method failed: " + e.getMessage());
                    e.printStackTrace();
                }

                System.out.println("After accept - visited: " + visitor.wasVisited());
                System.out.println("Visited type: " + visitor.getVisitedType());

                // Try alternative approach - check if statement has specific visit methods
                System.out.println("Available methods on statement:");
                for (java.lang.reflect.Method method : statement.getClass().getMethods()) {
                    if (method.getName().contains("accept") || method.getName().contains("visit")) {
                        System.out.println("  " + method.getName() + "(" + java.util.Arrays.toString(method.getParameterTypes()) + ")");
                    }
                }
                
            } catch (JSQLParserException e) {
                System.out.println("Parse error for '" + sql + "': " + e.getMessage());
            } catch (Exception e) {
                System.out.println("Error for '" + sql + "': " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
}
